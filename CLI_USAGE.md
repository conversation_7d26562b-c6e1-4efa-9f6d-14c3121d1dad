# CLI 使用指南

## 简化命令（推荐）

### 快速转换命令

最简单的使用方式，自动检测所有必要的文件和目录：

```bash
# 基本用法 - 自动检测 schemas 和 swagger.json
node dist/cli.js quick test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp

# 指定 Spring Boot 项目路径
node dist/cli.js quick test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp ./my-spring-project

# 自定义包名
node dist/cli.js quick test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp -p com.mycompany.movies

# 跳过部署
node dist/cli.js quick test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp --no-deploy

# 跳过 API 验证
node dist/cli.js quick test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp --no-validate
```

### 自动检测功能

快速命令会自动检测以下文件和目录：

1. **Schemas 目录**：在以下位置查找
   - `../Schemas`（相对于 BWP 文件）
   - `../../Schemas`
   - `./Schemas`
   - `../../../Schemas`

2. **swagger.json 文件**：在以下位置查找
   - `../Resources/swagger.json`
   - `../../Resources/swagger.json`
   - `./swagger.json`
   - `../swagger.json`

3. **默认设置**：
   - 输出目录：`./temp-output`
   - Spring Boot 项目：`./spring-boilerplate`
   - 包名：`com.example.movies`
   - 启用 API 验证（如果找到 swagger.json）
   - 启用部署到 Spring Boot 项目

## 完整命令（高级用户）

如果需要完全控制所有选项，可以使用完整的 `convert` 命令：

```bash
node dist/cli.js convert \
  -i test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp \
  -s test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas \
  -o temp-output \
  -p com.example.movies \
  --spring-boot-project spring-boilerplate \
  --validate-api \
  --swagger-json test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Resources/swagger.json
```

### 完整命令选项

- `-i, --input <path>`：BWP 文件路径（必需）
- `-o, --output <path>`：输出目录（默认：`./generated`）
- `-p, --package <name>`：Java 包名（默认：`com.example.app`）
- `-s, --schemas <path>`：XSD schemas 目录路径
- `--spring-boot-project <path>`：Spring Boot 项目路径
- `--swagger-json <path>`：TIBCO BW swagger.json 文件路径
- `--validate-api`：启用 API 一致性验证
- `--no-validation`：禁用 JSR-303 验证注解
- `--lombok`：使用 Lombok 注解
- `--no-jackson`：禁用 Jackson 注解
- `--no-constructors`：跳过构造函数生成
- `--no-tostring`：跳过 toString 方法生成

## 其他实用命令

### 验证 BWP 文件

```bash
node dist/cli.js validate -i path/to/file.bwp
```

### 仅生成模型类

```bash
node dist/cli.js generate-models -s path/to/schemas -o ./models -p com.example.models
```

### 验证 API 一致性

```bash
node dist/cli.js validate-api --spring-boot-project ./my-project --swagger-json ./swagger.json
```

## 使用建议

1. **首次使用**：推荐使用 `quick` 命令，它会自动处理大部分配置
2. **开发环境**：使用 `quick` 命令快速迭代和测试
3. **生产环境**：使用 `convert` 命令进行精确控制
4. **调试问题**：先使用 `validate` 命令检查 BWP 文件是否正确解析

## 示例工作流

```bash
# 1. 快速转换并部署
node dist/cli.js quick ./path/to/SearchMovies.bwp

# 2. 验证生成的代码
cd spring-boilerplate
mvn spring-boot:run

# 3. 测试 API
curl http://localhost:8080/movies?searchString=batman

# 4. 如果需要调整，重新生成
node dist/cli.js quick ./path/to/SearchMovies.bwp -p com.mycompany.api
```

这样的工作流程大大简化了从 TIBCO BW 到 Spring Boot 的转换过程。
